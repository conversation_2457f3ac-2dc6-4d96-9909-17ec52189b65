-- Convert the base-three number 1222₃ to its base-ten value
-- Prove that 1222₃ = 53₁₀

import Mathlib.Data.Nat.Basic
import Mathlib.Tactic.NormNum

-- Define base-three number 1222₃ using standard expansion
def base_three_1222_standard : ℕ := 1 * 3^3 + 2 * 3^2 + 2 * 3^1 + 2 * 3^0

-- Define base-three number 1222₃ using left-to-right accumulation
def base_three_1222_accumulation : ℕ := (((0 * 3 + 1) * 3 + 2) * 3 + 2) * 3 + 2

-- Main theorem: prove that 1222₃ = 53₁₀
theorem mathd_numbertheory_85 : base_three_1222_standard = 53 := by
  unfold base_three_1222_standard
  norm_num

-- Verification theorem: both methods give same result
theorem base_conversion_equivalence : base_three_1222_standard = base_three_1222_accumulation := by
  unfold base_three_1222_standard base_three_1222_accumulation
  norm_num

-- Auxiliary lemma: standard expansion equals 53
lemma standard_expansion_eq_53 : 1 * 3^3 + 2 * 3^2 + 2 * 3^1 + 2 * 3^0 = 53 := by
  norm_num

-- Auxiliary lemma: accumulation method equals 53
lemma accumulation_eq_53 : (((0 * 3 + 1) * 3 + 2) * 3 + 2) * 3 + 2 = 53 := by
  norm_num
