-- Proof content:
-- 1. [Problem Restatement] Given that n ≡ 0 (mod 3), show that S = (n + 4) + (n + 6) + (n + 8) leaves remainder 0 when divided by 9. 2. [Key Idea] Group the sum to expose a factor of 9, or work entirely in modular arithmetic. 3. [Proof] Style A – Direct factoring S = (n + 4) + (n + 6) + (n + 8) = 3n + 18 If n is a multiple of 3, write n = 3k. Then S = 3(3k) + 18 = 9k + 18 = 9(k + 2). Hence S is an integer multiple of 9, so the remainder upon division by 9 is 0. Style B – Modular arithmetic Since n ≡ 0 (mod 3), we have n ≡ 0 (mod 9) or n ≡ 3 (mod 9) or n ≡ 6 (mod 9). Compute S modulo 9 for each case: • If n ≡ 0: S ≡ (0+4)+(0+6)+(0+8) = 18 ≡ 0 (mod 9). • If n ≡ 3: S ≡ (3+4)+(3+6)+(3+8) = 27 ≡ 0 (mod 9). • If n ≡ 6: S ≡ (6+4)+(6+6)+(6+8) = 36 ≡ 0 (mod 9). In every case S ≡ 0 (mod 9). 4. [Conclusion] Whenever n is a multiple of 3, the sum (n+4)+(n+6)+(n+8) is divisible by 9, leaving remainder 0.
