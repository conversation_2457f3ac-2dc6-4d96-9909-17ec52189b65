-- Proof content:
-- 1. [Problem Restatement] Find the least positive integer n such that the two consecutive Euler values n² − n + 41 and (n+1)² − (n+1) + 41 have a common divisor larger than 1. 2. [Key Idea] Use <PERSON><PERSON><PERSON>’s algorithm to write the common divisor as gcd(n² − n + 41, 2n); any odd prime factor of this gcd must divide both n and the constant 41, forcing n to be a multiple of 41. 3. [Proof] Let p(n) = n² − n + 41 and d = gcd(p(n), p(n+1)). Since p(n+1) − p(n) = 2n, d = gcd(p(n), p(n+1)) = gcd(p(n), 2n). (1) Because p(n) is always odd (n² − n is even, +41 makes it odd), equation (1) shows d is also odd, so 2 ∤ d and every prime divisor of d divides n. Let an odd prime q | d. Then q | n and q | p(n). Write n = qk: p(n) = n² − n + 41 = q²k² − qk + 41 ≡ 41 (mod q), so q | 41. The only odd prime divisor of 41 is 41 itself; hence every common divisor >1 is a power of 41, and n must be a multiple of 41. The smallest positive such n is n = 41. Checking: p(41) = 41² − 41 + 41 = 41², p(42) = 42² − 42 + 41 = 41·43, so gcd(p(41), p(42)) = 41. No smaller positive n can work, proving 41 is minimal. 4. [Conclusion] The least positive integer for which p(n) and p(n+1) share a factor greater than 1 is n = 41.
