-- Proof content:
-- 1. [Problem Restatement] Given gcd(m,n)=8 and lcm(m,n)=112 for positive integers m,n, determine the minimum possible value of m+n. 2. [Key Idea] Write m=8a, n=8b with gcd(a,b)=1; the relation lcm·gcd = m·n then forces ab=14, so we need coprime factors of 14 whose sum is minimal. 3. [Proof] Let d = gcd(m,n) = 8. Then m = 8a and n = 8b for some positive integers a,b with gcd(a,b)=1. The fundamental identity lcm(m,n) · gcd(m,n) = m · n gives 112 · 8 = m · n = (8a)(8b) = 64ab. Hence 64ab = 896 ⇒ ab = 14. (1) Because gcd(a,b)=1, (a,b) must be a coprime factor pair of 14. The possibilities are (a,b) ∈ { (1,14), (2,7), (7,2), (14,1) }. Their corresponding sums a+b are 15, 9, 9, 15. The minimum is 9, achieved by (a,b) = (2,7) or (7,2). Therefore m + n = 8(a + b) = 8·9 = 72, which is the least possible value. 4. [Conclusion] The smallest possible value of m+n is 72.
