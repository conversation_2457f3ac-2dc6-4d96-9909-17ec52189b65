-- Proof content:
-- 1. [Problem Restatement] Find the greatest negative integer x such that 24x ≡ 15 (mod 1199). 2. [Key Idea] Compute the modular inverse of 24 modulo 1199, solve the linear congruence, then pick the solution closest to 0 from below. 3. [Proof] a) Existence of an inverse gcd(24, 1199) = 1 (since 1199 = 24·49 + 23 and 24 = 23·1 + 1), so 24 is invertible modulo 1199. b) Inverse of 24 modulo 1199 From the extended Euclidean algorithm: 1 = 24 − 23 = 24 − (1199 − 24·49) = 24·50 − 1199. Hence 24·50 ≡ 1 (mod 1199), so 24⁻¹ ≡ 50 (mod 1199). c) Solve the congruence 24x ≡ 15 ⟹ x ≡ 50·15 ≡ 750 (mod 1199). General solution: x = 750 + 1199k, k ∈ ℤ. d) Largest negative solution Take k = −1 to move 750 just below 0: x = 750 − 1199 = −449. For k = 0 we get 750 ≥ 0, and for k ≤ −2 we get x ≤ −1648 < −449, so −449 is the greatest negative integer satisfying the congruence. 4. [Conclusion] The largest negative integer x with 24x ≡ 15 (mod 1199) is −449.
