import Mathlib.Data.ZMod.Basic
import Mathlib.Tactic.NormNum

-- Problem: Find the residue n (0 ≤ n ≤ 46) satisfying 2n ≡ 15 (mod 47)
theorem mathd_numbertheory_99 : ∃! n : ℕ, n < 47 ∧ (2 * n : ZMod 47) = 15 := by
  -- Step 1: Prove that 2 has a modular inverse modulo 47
  have h_inv_exists : IsUnit (2 : ZMod 47) := by
    apply (ZMod.isUnit_iff_coprime 2 47).mpr
    rw [Nat.coprime_iff_gcd_eq_one]
    decide

  -- Step 2: Compute the modular inverse of 2 modulo 47 (which is 24)
  have h_inv_val : (2 : <PERSON>Mod 47)⁻¹ = 24 := by
    apply ZMod.inv_eq_of_mul_eq_one
    show (2 * 24 : ZMod 47) = 1
    decide

  -- Step 3: Apply the modular inverse to solve for n
  have h_solution : (2 * 31 : ZMod 47) = 15 := by
    decide

  -- Step 4: Compute 24 * 15 modulo 47 equals 31
  have h_computation : (24 * 15 : ZMod 47) = 31 := by
    decide

  -- Step 5: Verify that n = 31 satisfies the original congruence
  have h_verify : (2 * 31 : ZMod 47) = 15 := by
    exact h_solution

  -- Step 6: Prove uniqueness of the solution in the range [0, 46]
  use 31
  constructor
  · constructor
    · norm_num
    · exact h_solution
  · intro n hn
    have h_eq : (2 * n : ZMod 47) = (2 * 31 : ZMod 47) := by
      rw [hn.2, h_solution]
    have h_inj : (n : ZMod 47) = (31 : ZMod 47) := by
      apply (IsUnit.mul_right_inj h_inv_exists).mp
      exact h_eq
    have h_mod : n ≡ 31 [MOD 47] := by
      exact (ZMod.natCast_eq_natCast_iff n 31 47).mp h_inj
    have h_n_lt : n < 47 := hn.1
    have h_31_lt : 31 < 47 := by norm_num
    exact Nat.ModEq.eq_of_lt_of_lt h_mod h_n_lt h_31_lt
