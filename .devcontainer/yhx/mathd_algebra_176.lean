-- Problem: Show that (x + 1)²·x expands to x³ + 2x² + x

import Mathlib.Algebra.Ring.Basic
import Mathlib.Tactic.Ring

variable {R : Type*} [CommRing R]

theorem mathd_algebra_176 (x : R) : (x + 1)^2 * x = x^3 + 2*x^2 + x := by
  -- Step 1: Expand (x + 1)²
  have h1 : (x + 1)^2 = x^2 + 2*x + 1 := by ring
  -- Step 2: Multiply by x
  have h2 : (x^2 + 2*x + 1) * x = x^3 + 2*x^2 + x := by ring
  -- Step 3: Combine steps
  rw [h1]
  exact h2
