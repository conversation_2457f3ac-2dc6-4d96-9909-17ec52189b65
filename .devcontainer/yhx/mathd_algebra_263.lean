import Mathlib.Data.Real.Sqrt
import Mathlib.Data.Real.Basic
import Mathlib.Tactic

-- Problem: Solve for y in the equation √(19 + 3y) = 7
theorem mathd_algebra_263 : ∃! y : ℝ, Real.sqrt (19 + 3 * y) = 7 := by
  -- Show that y = 10 is the unique solution
  use 10
  constructor
  · -- Prove that y = 10 satisfies the equation
    simp only [Function.comp_apply]
    rw [Real.sqrt_eq_iff_mul_self_eq_of_pos (by norm_num : (0 : ℝ) < 7)]
    norm_num
  · -- Prove uniqueness
    intro y hy
    -- Square both sides: √(19 + 3y) = 7 ⇒ 19 + 3y = 49
    rw [Real.sqrt_eq_iff_mul_self_eq_of_pos (by norm_num : (0 : ℝ) < 7)] at hy
    -- Now hy : 7 * 7 = 19 + 3 * y, which simplifies to 49 = 19 + 3 * y
    -- So 3 * y = 30, hence y = 10
    norm_num at hy
    linarith
