import Mathlib.Data.Real.Sqrt
import Mathlib.Data.Real.Basic
import Mathlib.Analysis.SpecialFunctions.Pow.Real

-- Problem: Show that √1,000,000 - ³√1,000,000 = 900

theorem mathd_algebra_208 : Real.sqrt 1000000 - (1000000 : ℝ) ^ (1/3 : ℝ) = 900 := by
  -- Strategy: Use 10^6 representation
  -- Step 1: Show 1,000,000 = 10^6
  have h1 : (1000000 : ℝ) = (10 : ℝ)^6 := by norm_num

  -- Step 2: Show √(10^6) = 10^3
  have h2 : Real.sqrt ((10 : ℝ)^6) = (10 : ℝ)^3 := by
    rw [← Real.sqrt_sq (by norm_num : (0 : ℝ) ≤ (10 : ℝ)^3)]
    congr 1
    norm_num

  -- Step 3: Show ³√(10^6) = 10^2
  have h3 : ((10 : ℝ)^6) ^ (1/3 : ℝ) = (10 : ℝ)^2 := by
    rw [← Real.rpow_natCast (10 : ℝ) 6, ← Real.rpow_mul (by norm_num : (0 : ℝ) ≤ 10)]
    norm_num

  -- Step 4: Show 10^3 - 10^2 = 900
  have h4 : (10 : ℝ)^3 - (10 : ℝ)^2 = 900 := by norm_num

  -- Combine all steps
  rw [h1, h2, h3, h4]
