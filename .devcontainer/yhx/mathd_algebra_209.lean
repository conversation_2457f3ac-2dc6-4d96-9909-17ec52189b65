-- mathd_algebra_209: Inverse function composition problem
-- Given h = f⁻¹ with h(2)=10, h(10)=1, h(1)=2, find f(f(10))

import Mathlib.Logic.Function.Basic
import Mathlib.Data.Real.Basic

theorem mathd_algebra_209 (f : ℝ → ℝ) (h : ℝ → ℝ)
  (h_inv : Function.LeftInverse h f ∧ Function.RightInverse h f)
  (h2 : h 2 = 10) (h10 : h 10 = 1) (h1 : h 1 = 2) :
  f (f 10) = 1 := by
  -- SUBGOAL_001: Derive f(10) = 2 from h(2) = 10
  have f10_eq_2 : f 10 = 2 := by
    -- Since h is left inverse of f: h(f(x)) = x for all x
    -- From h(2) = 10, we get f(10) = 2 by applying f to both sides
    have h_left : Function.LeftInverse h f := h_inv.1
    -- h(2) = 10 means h(f(10)) = h(2) = 10, but h(f(10)) = 10 by left inverse property
    -- So we need: if h(a) = b then f(b) = a
    -- Since h is right inverse of f: f(h(x)) = x for all x
    have h_right : Function.RightInverse h f := h_inv.2
    -- From h(2) = 10 and right inverse property: f(h(2)) = 2, so f(10) = 2
    rw [← h2]
    exact h_right 2
  -- SUBGOAL_002: Derive f(2) = 1 from h(1) = 2
  have f2_eq_1 : f 2 = 1 := by
    -- Same approach: use right inverse property
    have h_right : Function.RightInverse h f := h_inv.2
    -- From h(1) = 2 and right inverse property: f(h(1)) = 1, so f(2) = 1
    rw [← h1]
    exact h_right 1
  -- SUBGOAL_003: Compute f(f(10)) using derived values
  calc f (f 10) = f 2 := by rw [f10_eq_2]
    _ = 1 := f2_eq_1
