-- Evaluate f(f(1)) for f(x) = 1/(x+2)
-- Expected result: f(f(1)) = 3/7

import Mathlib.Data.Rat.Defs
import Mathlib.Tactic.FieldSimp
import Mathlib.Tactic.NormNum

-- Define the function f(x) = 1/(x+2)
def f (x : ℚ) : ℚ := 1 / (x + 2)

-- Main theorem: f(f(1)) = 3/7
theorem mathd_algebra_270 : f (f 1) = 3 / 7 := by
  -- Step 1: Compute f(1)
  have h1 : f 1 = 1 / 3 := by
    unfold f
    simp
    norm_num
  -- Step 2: Compute f(f(1)) = f(1/3)
  have h2 : f (1 / 3) = 3 / 7 := by
    sorry
  -- Step 3: Combine the results
  rw [h1, h2]
