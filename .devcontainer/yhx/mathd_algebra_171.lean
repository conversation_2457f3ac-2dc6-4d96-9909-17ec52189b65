import Mathlib.Data.Real.Basic
import Mathlib.Tactic.NormNum.Basic

-- mathd_algebra_171: Evaluate f(1) for the linear function f(x) = 5x + 4

-- Define the linear function f(x) = 5x + 4
def f (x : ℝ) : ℝ := (5 : ℝ) * x + (4 : ℝ)

-- Main theorem: f(1) = 9
theorem mathd_algebra_171 : f 1 = 9 := by
  -- SUBGOAL_002: Substitute x = 1 into f(x) = 5x + 4
  unfold f
  -- SUBGOAL_003: Simplify 5·1 + 4 = 9
  norm_num
