import Mathlib.Data.Real.Basic
import Mathlib.Data.Real.Sqrt
import Mathlib.Algebra.GeomSum
import Mathlib.Tactic
import Mathlib.Tactic.Linarith

-- Problem: Find the positive number a such that 6, a, b and 1/b, a, 54 are geometric progressions

theorem mathd_algebra_184 : ∃! a : ℝ, a > 0 ∧
  (∃ b : ℝ, b > 0 ∧
    -- First geometric progression: 6, a, b
    (a^2 = 6 * b) ∧
    -- Second geometric progression: 1/b, a, 54
    (a^2 = 54 / b)) := by

  -- Use geometric progression property: square of middle term = product of neighbors
  use 3 * Real.sqrt 2
  constructor
  · -- Prove existence
    constructor
    · -- Prove a > 0
      apply mul_pos
      · norm_num
      · exact Real.sqrt_pos.mpr (by norm_num)
    · -- Prove the geometric progression conditions
      use 3  -- b = 3
      constructor
      · -- Prove b > 0
        norm_num
      · constructor
        · -- Prove a² = 6b for first progression
          simp only [pow_two]
          ring_nf
          rw [Real.sq_sqrt (by norm_num : (0 : ℝ) ≤ 2)]
          norm_num
        · -- Prove a² = 54/b for second progression
          simp only [pow_two]
          ring_nf
          rw [Real.sq_sqrt (by norm_num : (0 : ℝ) ≤ 2)]
          norm_num
  · -- Prove uniqueness
    intro a' ha'
    -- Extract conditions from ha'
    obtain ⟨ha'_pos, b', hb'_pos, h1, h2⟩ := ha'
    -- From h1 and h2: 6b' = 54/b' ⟹ b'² = 9 ⟹ b' = 3
    have hb'_eq : b' = 3 := by
      -- From h1: a'² = 6 * b', from h2: a'² = 54 / b'
      -- So 6 * b' = 54 / b'
      have h_eq : 6 * b' = 54 / b' := by rw [← h1, h2]
      -- Clear denominators: 6 * b' * b' = 54
      have h_clear : 6 * b' * b' = 54 := by
        field_simp [ne_of_gt hb'_pos] at h_eq
        exact h_eq
      -- So b'² = 9, hence b' = 3
      have h_sq : b' * b' = 9 := by
        have : 6 * (b' * b') = 54 := by rw [← mul_assoc]; exact h_clear
        have : b' * b' = 54 / 6 := by
          rw [← this]
          simp [mul_div_cancel₀]
        rw [this]
        norm_num
      -- Since b' > 0 and b'² = 9, we get b' = 3
      have h_pos_sqrt : b' = Real.sqrt (b' * b') := by
        rw [Real.sqrt_mul_self (le_of_lt hb'_pos)]
      rw [h_pos_sqrt, h_sq]
      norm_num
    -- Substitute back: a'² = 6 * 3 = 18 ⟹ a' = 3√2
    have ha'_eq : a'^2 = 18 := by
      rw [h1, hb'_eq]
      norm_num
    -- Since a' > 0, we get a' = √18 = 3√2
    have h_sqrt : a' = Real.sqrt 18 := by
      rw [← Real.sqrt_sq (le_of_lt ha'_pos), ha'_eq]
    rw [h_sqrt]
    -- Simplify √18 = √(9*2) = 3√2
    have : Real.sqrt 18 = Real.sqrt (9 * 2) := by norm_num
    rw [this]
    rw [Real.sqrt_mul (by norm_num : (0 : ℝ) ≤ 9) 2]
    have : Real.sqrt 9 = 3 := by
      rw [← Real.sqrt_sq (by norm_num : (0 : ℝ) ≤ 3)]
      norm_num
    rw [this]
