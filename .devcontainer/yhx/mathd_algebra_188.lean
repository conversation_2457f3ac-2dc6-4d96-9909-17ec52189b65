import Mathlib.Logic.Function.Basic
import Mathlib.Logic.Equiv.Defs

-- Problem: Given an invertible function f with f(2) = 4 and f⁻¹(2) = 4, compute f(f(2))
theorem mathd_algebra_188 [Nonempty ℕ] (f : ℕ → ℕ) (hf_bij : Function.Bijective f)
  (h1 : f 2 = 4) (h2 : Function.invFun f 2 = 4) : f (f 2) = 2 := by
  -- Subgoal 1: Prove f(4) = 2 from f⁻¹(2) = 4
  have h_f_4_eq_2 : f 4 = 2 := by
    -- Use the fact that Function.invFun f 2 = 4 means f 4 = 2
    have h_surj : Function.Surjective f := hf_bij.2
    have h_exists : ∃ a, f a = 2 := h_surj 2
    have h_inv_eq : f (Function.invFun f 2) = 2 := Function.invFun_eq h_exists
    rw [h2] at h_inv_eq
    exact h_inv_eq
  -- Subgoal 2: Prove f(f(2)) = f(4) by substitution
  have h_f_f_2_eq_f_4 : f (f 2) = f 4 := by
    rw [h1]
  -- Subgoal 3: Combine results to show f(f(2)) = 2
  rw [h_f_f_2_eq_f_4, h_f_4_eq_2]
