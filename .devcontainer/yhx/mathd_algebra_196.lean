import Mathlib.Data.Real.Basic
import Mathlib.Tactic.Linarith

-- Problem: Find the sum of all real numbers x that satisfy |2 - x| = 3

theorem mathd_algebra_196 :
  ∃ (s : ℝ), (∀ x : ℝ, |2 - x| = 3 → (x = -1 ∨ x = 5)) ∧ s = (-1) + 5 ∧ s = 4 := by
  use 4
  constructor
  · -- Prove solutions are exactly -1 and 5
    intro x hx
    -- Case analysis on |2 - x| = 3
    have h_cases : 2 - x = 3 ∨ 2 - x = -3 := by
      -- Use the fact that |a| = b iff a = b or a = -b when b > 0
      have h_pos : (0 : ℝ) < 3 := by linarith
      by_cases h : 2 - x ≥ 0
      · -- Case: 2 - x ≥ 0, so |2 - x| = 2 - x
        left
        have : |2 - x| = 2 - x := abs_of_nonneg h
        rw [this] at hx
        exact hx
      · -- Case: 2 - x < 0, so |2 - x| = -(2 - x)
        right
        have : |2 - x| = -(2 - x) := abs_of_neg (not_le.mp h)
        rw [this] at hx
        linarith
    cases h_cases with
    | inl h1 =>
      -- Case: 2 - x = 3, so x = -1
      left
      linarith
    | inr h2 =>
      -- Case: 2 - x = -3, so x = 5
      right
      linarith
  constructor
  · -- Prove s = (-1) + 5
    linarith
  · -- Prove s = 4
    rfl
