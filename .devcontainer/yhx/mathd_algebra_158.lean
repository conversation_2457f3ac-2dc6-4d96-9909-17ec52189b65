-- Proof content:
-- 1. [Problem Restatement] Find the smallest integer in a block of five consecutive even integers whose sum is 4 less than the sum of the first eight odd counting numbers. 2. [Key Idea] Translate “consecutive even/odd numbers” into an algebraic string, equate the two required sums, and solve the resulting linear equation. 3. [Proof] Algebraic proof. Let the five consecutive even integers be x, x + 2, x + 4, x + 6, x + 8 (x is even). Sum of these five numbers: S₁ = x + (x + 2) + (x + 4) + (x + 6) + (x + 8) = 5x + 20. The first eight odd positive integers are 1, 3, 5, 7, 9, 11, 13, 15. Sum of these eight numbers: S₂ = 1 + 3 + 5 + 7 + 9 + 11 + 13 + 15 = 8 · (1 + 15)/2 = 8 · 8 = 64. Given condition: S₁ = S₂ − 4 5x + 20 = 64 − 4 = 60 5x = 40 x = 8. Hence the smallest even integer is 8. (Alternative one-liner) Because consecutive odds sum to number·average, the first eight odds sum to 64; four less is 60, which divided equally among five even numbers (whose middle is even) locates the middle at 12 and the smallest at 8. 4. [Conclusion] The smallest of the five consecutive even integers is 8.
