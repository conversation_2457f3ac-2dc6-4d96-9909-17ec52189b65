import Mathlib.Data.Real.Basic
import Mathlib.Tactic.NormNum

-- Problem: The plumber's fee is modeled by C(h) = N + xh; given C(1) = 97 and C(5) = 265, find C(2).

theorem mathd_algebra_160 : ∃ (N x : ℝ),
  (N + x = (97 : ℝ)) ∧ (N + (5 : ℝ) * x = (265 : ℝ)) ∧ (N + (2 : ℝ) * x = (139 : ℝ)) := by
  -- Set up the linear system from given conditions and provide witnesses
  use 55, 42
  -- Verify the three equations by computation
  constructor
  · norm_num  -- 55 + 42 = 97
  constructor
  · norm_num  -- 55 + 5*42 = 265
  · norm_num  -- 55 + 2*42 = 139
