-- Problem: 40 calories constitute 2% of a person's daily caloric need; find the whole daily caloric requirement.
-- Answer: 2000 calories

import Mathlib.Data.Real.Basic
import Mathlib.Tactic.FieldSimp
import Mathlib.Tactic.NormNum

theorem mathd_algebra_24 : ∃ D : ℝ, 40 = 0.02 * D ∧ D = 2000 := by
  -- SUBGOAL_001: Define daily caloric requirement variable D
  use 2000
  constructor
  · -- SUBGOAL_002: Set up equation 40 = 0.02 × D
    -- SUBGOAL_003: Solve D = 40 / 0.02
    -- SUBGOAL_004: Show D = 2000
    norm_num
  · -- SUBGOAL_005: Verify alternate calculation 40 × 50 = 2000
    rfl
