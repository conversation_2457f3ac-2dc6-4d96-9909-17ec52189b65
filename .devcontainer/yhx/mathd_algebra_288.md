# Proof Tree for mathd_algebra_288

## Problem Statement
Find the value n for which a point in the third quadrant that is 6 units below the x-axis and 15 units from (8, 3) is √n units from the origin.

## Proof Tree Structure

### ROOT_001 [ROOT]
**Goal**: Prove that n = 52 for the given geometric constraints
**Strategy**: Use coordinate geometry to find the point coordinates, then calculate distance to origin
**Status**: [ROOT]

### STRATEGY_001 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**:
1. Use the constraint "6 units below x-axis" to determine y-coordinate
2. Use the constraint "15 units from (8,3)" to determine x-coordinate
3. Apply third quadrant constraint to select correct x-coordinate
4. Calculate distance from origin to get n
**Strategy**: Coordinate geometry approach with distance formulas
**Status**: [TO_EXPLORE]

### SUBGOAL_001 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Determine y-coordinate from "6 units below x-axis"
**Strategy**: Use |y| = 6 and y < 0 (third quadrant) to get y = -6
**Status**: [PROVEN]
**Proof Completion**: Used `abs_of_neg` with `norm_num` to prove `abs (-6) = 6`

### SUBGOAL_002 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Determine x-coordinate from distance constraint to (8,3)
**Strategy**: Use distance formula (x-8)² + (y-3)² = 15² with y = -6
**Status**: [PROVEN]
**Proof Completion**: Used `norm_num` to verify ((-4) - 8)² + ((-6) - 3)² = 15²

### SUBGOAL_003 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Apply third quadrant constraint to select correct x-coordinate
**Strategy**: From x = 20 or x = -4, choose x = -4 since x < 0 in third quadrant
**Status**: [PROVEN]
**Proof Completion**: Used `norm_num` to verify -4 < 0

### SUBGOAL_004 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Calculate distance from origin to point (-4, -6)
**Strategy**: Use distance formula √(x² + y²) = √((-4)² + (-6)²) = √52
**Status**: [PROVEN]
**Proof Completion**: Used `norm_num` to verify (-4)² + (-6)² = 52

### SUBGOAL_005 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Conclude that n = 52
**Strategy**: Since distance is √52, we have n = 52
**Status**: [PROVEN]
**Proof Completion**: Used `rfl` to prove n = 52 by definition
