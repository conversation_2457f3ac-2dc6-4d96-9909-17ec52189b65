import Mathlib.Data.Real.Basic
import Mathlib.Data.Real.Sqrt
import Mathlib.Algebra.Field.Basic
import Mathlib.Tactic

-- Problem: Find the sum of the two real solutions of (x + 3)² = 121

theorem mathd_algebra_215 : ∃ (x₁ x₂ : ℝ), (x₁ + 3)^2 = 121 ∧ (x₂ + 3)^2 = 121 ∧ x₁ ≠ x₂ ∧ x₁ + x₂ = -6 := by
  -- SUBGOAL_001: Solve equation (x + 3)² = 121
  -- The two solutions are x = 8 and x = -14
  use 8, -14
  constructor
  · -- Prove (8 + 3)^2 = 121
    norm_num
  constructor
  · -- Prove (-14 + 3)^2 = 121
    norm_num
  constructor
  · -- Prove 8 ≠ -14
    norm_num
  · -- Prove 8 + (-14) = -6
    norm_num

-- Helper lemma: The two solutions are x = 8 and x = -14
lemma solutions_are_8_and_neg14 : (8 + 3)^2 = 121 ∧ (-14 + 3)^2 = 121 := by
  -- SUBGOAL_002: Verify both solutions satisfy the equation
  constructor
  · norm_num  -- (8 + 3)^2 = 11^2 = 121
  · norm_num  -- (-14 + 3)^2 = (-11)^2 = 121

-- Helper lemma: Sum calculation
lemma sum_is_neg6 : (8 : ℝ) + (-14) = -6 := by
  -- SUBGOAL_003: Calculate sum x₁ + x₂
  norm_num
