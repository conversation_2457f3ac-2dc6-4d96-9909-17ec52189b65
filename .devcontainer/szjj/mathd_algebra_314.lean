import Mathlib.Data.Rat.Defs
import Mathlib.Algebra.Ring.Defs
import Mathlib.Tactic.Ring
import Mathlib.Tactic.NormNum

-- MATHD Algebra 314: Evaluate (1/4)^(n+1) * 2^(2n) for n=11

theorem mathd_algebra_314 (n : ℤ) : (1 / 4 : ℚ) ^ (n + 1) * 2 ^ (2 * n) = 1 / 4 := by
  -- Step 1: Rewrite 1/4 as 2^(-2)
  have step1 : (1 / 4 : ℚ) ^ (n + 1) * 2 ^ (2 * n) = (2 ^ (-2 : ℤ) : ℚ) ^ (n + 1) * 2 ^ (2 * n) := by
    congr 1
    norm_num

  -- Step 2: Apply power of a power rule: (2^(-2))^(n+1) = 2^(-2(n+1))
  have step2 : (2 ^ (-2 : ℤ) : ℚ) ^ (n + 1) = (2 : ℚ) ^ ((-2) * (n + 1)) := by
    rw [← zpow_mul]

  -- Step 3: Distribute the exponent: -2(n+1) = -2n-2
  have step3 : (-2) * (n + 1) = -2 * n - 2 := by
    ring

  -- Step 4: Combine exponents: 2^(-2n-2) * 2^(2n) = 2^((-2n-2)+2n)
  have step4 : (2 : ℚ) ^ (-2 * n - 2) * 2 ^ (2 * n) = (2 : ℚ) ^ ((-2 * n - 2) + 2 * n) := by
    have h_nonzero : (2 : ℚ) ≠ 0 := by norm_num
    rw [← zpow_add₀ h_nonzero]

  -- Step 5: Simplify the exponent: (-2n-2)+2n = -2
  have step5 : (-2 * n - 2) + 2 * n = -2 := by
    ring

  -- Step 6: Convert back to fraction form: 2^(-2) = 1/4
  have step6 : (2 : ℚ) ^ (-2 : ℤ) = 1 / 4 := by
    norm_num

  -- Step 7: Combine all steps
  rw [step1, step2, step3, step4, step5, step6]
