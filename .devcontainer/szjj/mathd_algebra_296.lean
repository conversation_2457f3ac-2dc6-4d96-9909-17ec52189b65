-- Problem: A 3491 × 3491 square is altered to (3491 − 60) × (3491 + 60); determine the resulting change in area.

import Mathlib.Data.Real.Basic
import Mathlib.Algebra.Ring.Basic
import Mathlib.Tactic.Ring

-- Main theorem: The change in area is -3600
theorem mathd_algebra_296 : (3491 - 60) * (3491 + 60) - 3491^2 = -3600 := by
  -- Strategy 1: Algebraic approach using difference of squares
  -- Step 1: Apply difference of squares identity
  have h1 : (3491 - 60) * (3491 + 60) = 3491^2 - 60^2 := by
    ring

  -- Step 2: Calculate the change in area
  have h2 : (3491^2 - 60^2) - 3491^2 = -60^2 := by
    ring

  -- Step 3: Evaluate -60^2 = -3600
  have h3 : -60^2 = -3600 := by
    norm_num

  -- Combine all steps
  simp [h1, h2, h3]
