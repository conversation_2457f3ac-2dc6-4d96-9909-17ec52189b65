import Mathlib.Data.Real.Basic
import Mathlib.Data.Real.Sqrt
import Mathlib.Analysis.SpecialFunctions.Trigonometric.Basic
import Mathlib.Tactic.Linarith

-- Helper lemma: |a - b|² = 1 - 2ab when a² + b² = 1
lemma abs_diff_sq (a b : ℝ) (h : a^2 + b^2 = 1) : |a - b|^2 = 1 - 2 * a * b := by
  rw [sq_abs, sub_sq]
  linarith [h]

-- Helper lemma: ab ≤ 1/2 when a² + b² = 1
lemma ab_upper_bound (a b : ℝ) (h : a^2 + b^2 = 1) : a * b ≤ 1/2 := by
  have h_nonneg : 0 ≤ (a - b)^2 := sq_nonneg _
  rw [sub_sq] at h_nonneg
  linarith [h]

-- Main theorem: For real numbers a, b with a² + b² = 1, prove ab + |a - b| ≤ 1
theorem algebra_sqineq_unitcircle (a b : ℝ) (h : a^2 + b^2 = 1) : a * b + |a - b| ≤ 1 := by
  -- Strategy 3: Simple direct approach using basic inequalities
  -- Use the fact that for points on the unit circle, we have simple bounds

  -- First establish that |a| ≤ 1 and |b| ≤ 1
  have bounds_a : |a| ≤ 1 := by
    have h1 : a^2 ≤ 1 := by
      rw [← h]
      exact le_add_of_nonneg_right (sq_nonneg b)
    rwa [← sq_le_one_iff_abs_le_one]

  have bounds_b : |b| ≤ 1 := by
    have h1 : b^2 ≤ 1 := by
      rw [← h]
      exact le_add_of_nonneg_left (sq_nonneg a)
    rwa [← sq_le_one_iff_abs_le_one]

  -- Use the key insight: the maximum value of ab + |a - b| on the unit circle is 1
  -- This can be achieved at specific points like (1, 0) where ab + |a - b| = 0 + 1 = 1
  -- We prove this by showing that ab + |a - b| ≤ max(ab + |a - b|) = 1

  -- The proof uses the fact that |a - b| ≤ |a| + |b| ≤ 2
  -- But we need a tighter bound using the constraint a² + b² = 1

  -- Key observation: ab + |a - b| = ab + |a - b|
  -- We can bound this by considering the geometry of the unit circle

  -- For any point (a, b) on the unit circle:
  -- Case 1: If ab ≥ 0, then a and b have the same sign
  -- Case 2: If ab < 0, then a and b have opposite signs

  by_cases h_ab_sign : 0 ≤ a * b
  · -- Case 1: ab ≥ 0 (same sign)
    -- In this case, |a - b| ≤ |a| + |b| ≤ 2, but we can do better
    -- Since a² + b² = 1 and ab ≥ 0, we have constraints on |a - b|
    -- The maximum of ab + |a - b| in this case occurs at boundary points
    -- We use the fact that ab ≤ 1/2 (from AM-GM: 2ab ≤ a² + b² = 1)
    have ab_bound : a * b ≤ 1/2 := by
      have h_amgm : 2 * a * b ≤ a^2 + b^2 := by
        have h_nonneg : 0 ≤ (a - b)^2 := sq_nonneg _
        rw [sub_sq] at h_nonneg
        linarith
      rw [h] at h_amgm
      linarith

    -- For the unit circle, |a - b|² = (a - b)² = a² - 2ab + b² = 1 - 2ab
    -- So |a - b| = √(1 - 2ab)
    -- We need ab + √(1 - 2ab) ≤ 1
    -- This is maximized when ab = 0, giving 0 + √1 = 1
    -- For ab > 0, the value is smaller
    have h_bound_case1 : a * b + |a - b| ≤ 1 := by
      -- Use the identity |a - b|² = 1 - 2ab
      have abs_sq : |a - b|^2 = 1 - 2 * a * b := by
        rw [sq_abs, sub_sq]
        linarith [h]

      -- The function f(t) = t + √(1 - 2t) for t ≥ 0 achieves maximum 1 at t = 0
      -- Since ab ≥ 0, we have f(ab) ≤ f(0) = 1
      by_cases h_zero : a * b = 0
      · -- When ab = 0, we have |a - b|² = 1, so |a - b| = 1
        -- Thus ab + |a - b| = 0 + 1 = 1
        rw [h_zero]
        simp
        have h_abs_one : |a - b| = 1 := by
          have h_sq_one : |a - b|^2 = 1 := by
            rw [abs_sq, h_zero]
            simp
          rw [← Real.sqrt_sq (abs_nonneg (a - b)), h_sq_one, Real.sqrt_one]
        rw [h_abs_one]
      · -- When ab > 0, we have |a - b|² = 1 - 2ab < 1, so |a - b| < 1
        -- The function f(t) = t + √(1 - 2t) is decreasing for t > 0
        -- So f(ab) < f(0) = 1
        have h_pos : 0 < a * b := lt_of_le_of_ne h_ab_sign h_zero
        -- We use the mathematical fact that f(t) = t + √(1 - 2t) ≤ 1 for t ≥ 0
        -- with equality only at t = 0
        sorry -- This requires showing the function is decreasing
    exact h_bound_case1

  · -- Case 2: ab < 0 (opposite signs)
    push_neg at h_ab_sign
    -- When ab < 0, we have |a - b|² = 1 - 2ab > 1, so |a - b| > 1
    -- But ab < 0, so ab + |a - b| could still be ≤ 1
    -- The minimum value of ab on the unit circle is -1/2 (achieved at (±1/√2, ∓1/√2))
    -- At ab = -1/2, we have |a - b|² = 1 + 1 = 2, so |a - b| = √2
    -- Thus ab + |a - b| = -1/2 + √2 ≈ -0.5 + 1.41 ≈ 0.91 < 1
    have h_bound_case2 : a * b + |a - b| ≤ 1 := by
      -- Use the identity |a - b|² = 1 - 2ab
      have abs_sq : |a - b|^2 = 1 - 2 * a * b := by
        rw [sq_abs, sub_sq]
        linarith [h]

      -- Since ab < 0, we have 1 - 2ab > 1, so |a - b| > 1
      -- The minimum value of ab is -1/2, so the maximum value of |a - b| is √2
      -- We need to show ab + |a - b| ≤ 1
      -- Since ab ≥ -1/2 and |a - b| = √(1 - 2ab), we have
      -- ab + |a - b| = ab + √(1 - 2ab)
      -- This function is maximized at ab = 0 (boundary case) where it equals 1
      -- For ab < 0, the function value is < 1
      have ab_lower : -1/2 ≤ a * b := by
        -- The minimum of ab on unit circle is -1/2
        have h_bound : (a * b)^2 ≤ (1/2)^2 := by
          -- From Cauchy-Schwarz: (ab)² ≤ (a² + b²)/4 · 4 = 1/4 · 4 = 1
          -- But we need the tighter bound (ab)² ≤ 1/4
          -- This follows from 4(ab)² ≤ (a² + b²)² = 1
          -- Actually, we use 2|ab| ≤ a² + b² = 1, so |ab| ≤ 1/2
          have h_amgm : 2 * |a * b| ≤ a^2 + b^2 := by
            rw [abs_mul]
            have h_nonneg : 0 ≤ (|a| - |b|)^2 := sq_nonneg _
            rw [sub_sq] at h_nonneg
            have h_eq : |a|^2 + |b|^2 = a^2 + b^2 := by simp [sq_abs]
            rw [← h_eq] at h_nonneg
            linarith
          rw [h] at h_amgm
          have h_abs_bound : |a * b| ≤ 1/2 := by linarith
          rw [abs_le] at h_abs_bound
          exact h_abs_bound.1
        exact ab_lower

      -- Now we use the fact that f(t) = t + √(1 - 2t) for t ∈ [-1/2, 0] is ≤ 1
      -- At t = -1/2: -1/2 + √2 ≈ 0.91 < 1
      -- At t = 0: 0 + 1 = 1
      -- The function is increasing on [-1/2, 0], so max value is 1
      sorry -- This requires detailed analysis of the function
    exact h_bound_case2
