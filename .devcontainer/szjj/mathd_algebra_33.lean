-- Problem: Given 2x = 5y and 7y = 10z, find z/x.

import Mathlib.Data.Real.Basic
import Mathlib.Algebra.Field.Basic
import Mathlib.Tactic.FieldSimp
import Mathlib.Tactic.Linarith
import Mathlib.Tactic.Ring

-- Main theorem: Given the constraints, z/x = 7/25
theorem mathd_algebra_33 (x y z : ℝ) (hx : x ≠ 0) (h1 : 2 * x = 5 * y) (h2 : 7 * y = 10 * z) :
  z / x = 7 / 25 := by
  -- Step 1: From 2x = 5y, derive y = (2/5)x
  have step1 : y = (2 / 5) * x := by
    linarith [h1]

  -- Step 2: Substitute y into 7y = 10z to get z = (7/25)x
  have step2 : z = (7 / 25) * x := by
    linarith [step1, h2]

  -- Step 3: Show that z/x = 7/25
  have step3 : z / x = 7 / 25 := by
    rw [step2]
    field_simp [hx]
    ring

  exact step3
