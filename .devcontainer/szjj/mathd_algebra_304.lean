import Mathlib.Data.Nat.Basic
import Mathlib.Tactic.Ring
import Mathlib.Tactic.NormNum

-- MathD Algebra 304: Show, by mental arithmetic, that 91² = 8281
theorem mathd_algebra_304 : (91 : ℕ)^2 = 8281 := by
  -- Method A: Expand (90 + 1)²
  have method_a : (90 + 1 : ℕ)^2 = 8281 := by
    -- Use binomial expansion (a + b)² = a² + 2ab + b²
    -- (90 + 1)² = 90² + 2·90·1 + 1²
    have expansion : (90 + 1 : ℕ)^2 = 90^2 + 2 * 90 * 1 + 1^2 := by
      ring
    -- Calculate 90² + 2·90·1 + 1² = 8100 + 180 + 1 = 8281
    have calculation : 90^2 + 2 * 90 * 1 + 1^2 = 8281 := by
      norm_num
    rw [expansion, calculation]

  -- Method B: Expand (100 - 9)²
  have method_b : (100 - 9 : ℕ)^2 = 8281 := by
    -- Use binomial expansion (a - b)² = a² - 2ab + b²
    -- (100 - 9)² = 100² - 2·100·9 + 9²
    have expansion : (100 - 9 : ℕ)^2 = 100^2 - 2 * 100 * 9 + 9^2 := by
      ring
    -- Calculate 100² - 2·100·9 + 9² = 10000 - 1800 + 81 = 8281
    have calculation : 100^2 - 2 * 100 * 9 + 9^2 = 8281 := by
      norm_num
    rw [expansion, calculation]

  -- Show 91 = 90 + 1
  have eq_method_a : (91 : ℕ) = 90 + 1 := by
    norm_num

  -- Use Method A to conclude
  rw [eq_method_a]
  exact method_a
