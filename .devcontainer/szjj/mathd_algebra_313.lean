import Mathlib.Data.Complex.Basic
import Mathlib.Data.Complex.Exponential
import Mathlib.Tactic.Ring
import Mathlib.Tactic.FieldSimp

-- MathD Algebra 313: Complex Number Division
-- Problem: Compute I given V = 1+i and Z = 2-i via V = IZ
-- Find I = V/Z = (1+i)/(2-i) and express in the form a + bi

-- Define the given complex numbers
def V : ℂ := 1 + Complex.I
def Z : ℂ := 2 - Complex.I

-- Main theorem: I = V/Z = 1/5 + 3/5 * I
theorem mathd_algebra_313 : V / Z = (1 : ℂ) / 5 + (3 : ℂ) / 5 * Complex.I := by
  -- Direct computation using conjugate multiplication
  unfold V Z
  -- (1 + i) / (2 - i) = (1 + i)(2 + i) / ((2 - i)(2 + i))
  have h1 : (2 - Complex.I) ≠ 0 := by simp [Complex.ext_iff]
  rw [← mul_div_mul_right (1 + Complex.I) (2 - Complex.I) (by simp [Complex.ext_iff] : (2 + Complex.I) ≠ 0)]
  -- Compute numerator: (1 + i)(2 + i) = 1 + 3i
  have num : (1 + Complex.I) * (2 + Complex.I) = 1 + 3 * Complex.I := by ring_nf; simp [Complex.I_sq]; ring
  -- Compute denominator: (2 - i)(2 + i) = 5
  have den : (2 - Complex.I) * (2 + Complex.I) = 5 := by ring_nf; simp [Complex.I_sq]; ring
  rw [num, den]
  field_simp

-- Helper lemma: Setup complex division
lemma complex_division_setup : V / Z = (1 + Complex.I) / (2 - Complex.I) := by
  unfold V Z
  rfl

-- Helper lemma: Conjugate multiplication
lemma conjugate_multiplication :
  (1 + Complex.I) / (2 - Complex.I) =
  ((1 + Complex.I) * (2 + Complex.I)) / ((2 - Complex.I) * (2 + Complex.I)) := by
  -- This is multiplying by (2+i)/(2+i) = 1
  have h : (2 + Complex.I) ≠ 0 := by simp [Complex.ext_iff]
  rw [← mul_div_mul_right (1 + Complex.I) (2 - Complex.I) h]

-- Helper lemma: Numerator calculation
lemma numerator_calc : (1 + Complex.I) * (2 + Complex.I) = 1 + 3 * Complex.I := by
  ring_nf
  simp [Complex.I_sq]
  ring

-- Helper lemma: Denominator calculation
lemma denominator_calc : (2 - Complex.I) * (2 + Complex.I) = 5 := by
  ring_nf
  simp [Complex.I_sq]
  ring

-- Helper lemma: Final simplification
lemma final_simplification : (1 + 3 * Complex.I) / 5 = (1 : ℂ) / 5 + (3 : ℂ) / 5 * Complex.I := by
  field_simp

-- Verification lemma: Check that I * Z = V
lemma verification : ((1 : ℂ) / 5 + (3 : ℂ) / 5 * Complex.I) * Z = V := by
  unfold V Z
  ring_nf
  simp [Complex.I_sq]
  ring
