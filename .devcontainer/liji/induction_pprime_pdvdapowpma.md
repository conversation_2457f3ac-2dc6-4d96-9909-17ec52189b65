# Proof Tree: Prime p divides a^p - a

## ROOT_001 [ROOT]
**Theorem**: For every prime p and positive integer a, p divides a^p - a
**Status**: [ROOT]
**Goal**: Prove ∀ (p : ℕ) (a : ℕ), Nat.Prime p → a > 0 → p ∣ (a^p - a)

## STRATEGY_001 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**: Use binomial theorem approach with modular arithmetic
**Strategy**: Prove (x + y)^p ≡ x^p + y^p (mod p) using binomial coefficients divisibility, then telescope with x = a, y = 1
**Status**: [TO_EXPLORE]

### SUBGOAL_001 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Handle case when p | a (trivial case)
**Strategy**: If p divides a, then p divides a^p, so p divides a^p - a
**Tactic Details**: Use dvd_pow to show p ∣ a^p, then dvd_sub to conclude p ∣ (a^p - a)
**Proof Completion**: Used h.pow (Nat.Prime.pos hp).ne' and Nat.dvd_sub
**Status**: [PROVEN]

### SUBGOAL_002 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Prove binomial coefficient divisibility
**Strategy**: For prime p and 1 ≤ k ≤ p-1, prove p ∣ (p choose k)
**Status**: [TO_EXPLORE]

### SUBGOAL_003 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Establish key congruence (x + y)^p ≡ x^p + y^p (mod p)
**Strategy**: Use binomial theorem and divisibility of interior coefficients
**Status**: [TO_EXPLORE]

### SUBGOAL_004 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Apply telescoping argument
**Strategy**: Use (a + 1)^p ≡ a^p + 1 (mod p) and telescope from a down to 0
**Status**: [TO_EXPLORE]

## STRATEGY_002 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**: Alternative group-theoretic proof using Fermat's Little Theorem
**Strategy**: Use multiplicative group (ℤ/pℤ)× has order p-1, so a^(p-1) ≡ 1 (mod p) for p∤a
**Status**: [TO_EXPLORE]

### SUBGOAL_005 [SUBGOAL]
**Parent Node**: STRATEGY_002
**Goal**: Handle case when p | a
**Strategy**: If p divides a, then trivially p divides a^p - a
**Status**: [TO_EXPLORE]

### SUBGOAL_006 [SUBGOAL]
**Parent Node**: STRATEGY_002
**Goal**: Apply Fermat's Little Theorem
**Strategy**: For p∤a, use a^(p-1) ≡ 1 (mod p), multiply by a to get a^p ≡ a (mod p)
**Tactic Details**: Use ZMod.pow_card and Nat.modEq_iff_dvd' to convert ZMod equality to natural number divisibility
**Proof Completion**: Used ZMod.pow_card theorem and converted via modular arithmetic
**Status**: [PROVEN]
