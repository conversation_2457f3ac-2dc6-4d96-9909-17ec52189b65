# Proof Tree: n! < n^(n-1) for n ≥ 3

## Node Structure

### ROOT_001 [ROOT]
- **Theorem**: For every integer n ≥ 3, prove that n! < n^(n-1)
- **Strategy**: Mathematical induction on n
- **Status**: [ROOT]

### STRATEGY_001 [STRATEGY]
- **Parent Node**: ROOT_001
- **Detailed Plan**: Use mathematical induction with base case n=3 and inductive step for k+1
- **Strategy**: Induction proof using decomposition n! = n·(n-1)! and base enlargement property
- **Status**: [PROVEN]
- **Proof Completion**: Successfully implemented using Nat.le_induction with base case and inductive step

### SUBGOAL_001 [SUBGOAL]
- **Parent Node**: STRATEGY_001
- **Goal**: Base case verification: 3! < 3^(3-1)
- **Strategy**: Direct calculation: 3! = 6 and 3^2 = 9, verify 6 < 9
- **Status**: [PROVEN]
- **Proof Completion**: Used `norm_num` tactic for direct numerical computation

### SUBGOAL_002 [SUBGOAL]
- **Parent Node**: STRATEGY_001
- **Goal**: Inductive step: Assume k! < k^(k-1) for k ≥ 3, prove (k+1)! < (k+1)^k
- **Strategy**: Use (k+1)! = (k+1)·k! and apply induction hypothesis with base enlargement
- **Status**: [PROVEN]
- **Proof Completion**: Used Nat.factorial_succ, Nat.mul_lt_mul_of_pos_left, Nat.pow_le_pow_left, and calc chain

### SUBGOAL_002_1 [SUBGOAL]
- **Parent Node**: SUBGOAL_002
- **Goal**: Show (k+1)·k! < (k+1)·k^(k-1) using induction hypothesis
- **Strategy**: Apply induction hypothesis k! < k^(k-1) and multiply both sides by (k+1)
- **Status**: [PROVEN]
- **Proof Completion**: Used Nat.mul_lt_mul_of_pos_left with induction hypothesis

### SUBGOAL_002_2 [SUBGOAL]
- **Parent Node**: SUBGOAL_002
- **Goal**: Show (k+1)·k^(k-1) ≤ (k+1)·(k+1)^(k-1) = (k+1)^k
- **Strategy**: Use monotonicity: k < k+1 implies k^(k-1) ≤ (k+1)^(k-1)
- **Status**: [PROVEN]
- **Proof Completion**: Used Nat.pow_le_pow_left and Nat.pow_succ' for power calculation

### SUBGOAL_003 [SUBGOAL]
- **Parent Node**: STRATEGY_001
- **Goal**: Combine base case and inductive step to complete proof
- **Strategy**: Apply mathematical induction principle
- **Status**: [PROVEN]
- **Proof Completion**: Completed via Nat.le_induction framework
