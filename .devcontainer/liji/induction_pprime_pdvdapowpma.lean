import Mathlib.Data.Nat.Prime.Basic
import Mathlib.Data.Nat.ModEq
import Mathlib.Algebra.Divisibility.Basic
import Mathlib.FieldTheory.Finite.Basic

-- Theorem: For every prime p and positive integer a, p divides a^p - a
theorem prime_divides_pow_sub_self (p a : ℕ) (hp : Nat.Prime p) : p ∣ (a^p - a) := by
  -- Case analysis: either p divides a or it doesn't
  by_cases h : p ∣ a
  · -- Case 1: p | a (trivial case)
    -- If p divides a, then p divides a^p, so p divides a^p - a
    have h_pow : p ∣ a^p := h.pow (Nat.Prime.pos hp).ne'
    exact Nat.dvd_sub h_pow h
  · -- Case 2: p ∤ a (use <PERSON><PERSON><PERSON>'s Little Theorem approach)
    -- Use ZMod.pow_card: for any a : ZMod p, we have a^p = a
    haveI : Fact (Nat.Prime p) := ⟨hp⟩
    have h1 : (a : ZMod p) ^ p = (a : ZMod p) := ZMod.pow_card (a : ZMod p)
    -- This means (a^p : ZMod p) = (a : ZMod p), so a^p ≡ a (mod p)
    have h2 : a ^ p ≡ a [MOD p] := by
      rw [← ZMod.eq_iff_modEq_nat]
      simp only [Nat.cast_pow]
      exact h1
    -- From a^p ≡ a (mod p), we get p | (a^p - a)
    have h3 : a ≤ a ^ p := Nat.le_self_pow (Nat.Prime.pos hp).ne' a
    rw [← Nat.modEq_iff_dvd' h3]
    exact h2.symm
