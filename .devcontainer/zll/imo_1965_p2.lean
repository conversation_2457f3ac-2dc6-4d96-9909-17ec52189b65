import Mathlib.LinearAlgebra.Matrix.Gershgorin
import Mathlib.LinearAlgebra.Matrix.Nondegenerate
import Mathlib.Data.Matrix.Basic
import Mathlib.Data.Real.Basic
import Mathlib.Data.Finset.Basic

-- IMO 1965 Problem 2: Homogeneous linear system with specific matrix properties
theorem imo_1965_p2 [DecidableEq (Fin 3)] (A : Matrix (Fin 3) (Fin 3) ℝ)
  (h_diag_pos : ∀ i, 0 < A i i)
  (h_off_diag_neg : ∀ i j, i ≠ j → A i j < 0)
  (h_row_sum_pos : ∀ i, 0 < ∑ j, A i j) :
  ∀ x : Fin 3 → ℝ, (∀ i, ∑ j, A i j * x j = 0) → (∀ i, x i = 0) := by

  -- Strategy 1: Use strict diagonal dominance and Levy-Desplanques theorem
  intro x hx

  -- SUBGOAL_001: Establish strict diagonal dominance condition
  have h_strict_diag_dom : ∀ i, A i i > ∑ j ∈ Finset.univ.erase i, |A i j| := by
    intro i
    -- From h_row_sum_pos: A i i + ∑_{j≠i} A i j > 0
    have h_pos := h_row_sum_pos i
    rw [← Finset.add_sum_erase Finset.univ (fun j => A i j) (Finset.mem_univ i)] at h_pos
    -- Since A i j < 0 for j ≠ i, we have |A i j| = -(A i j)
    have h_abs : ∑ j ∈ Finset.univ.erase i, |A i j| = ∑ j ∈ Finset.univ.erase i, -(A i j) := by
      apply Finset.sum_congr rfl
      intro j hj
      rw [abs_of_neg (h_off_diag_neg i j (Finset.ne_of_mem_erase hj).symm)]
    rw [h_abs]
    have h_neg : ∑ j ∈ Finset.univ.erase i, -(A i j) = -(∑ j ∈ Finset.univ.erase i, A i j) := by
      rw [Finset.sum_neg_distrib]
    rw [h_neg]
    -- Therefore: A i i > -∑_{j≠i} A i j = ∑_{j≠i} |A i j|
    linarith

  -- SUBGOAL_002: Apply Gershgorin theorem (matrix is nonsingular)
  have h_nonsingular : A.det ≠ 0 := by
    apply det_ne_zero_of_sum_row_lt_diag
    intro k
    -- Convert from A k k > ∑ |A k j| to ∑ ‖A k j‖ < ‖A k k‖
    have h_lt := h_strict_diag_dom k
    rw [gt_iff_lt] at h_lt
    -- Show ∑ j ∈ Finset.univ.erase k, ‖A k j‖ = ∑ j ∈ Finset.univ.erase k, |A k j|
    have h_norm_eq : ∑ j ∈ Finset.univ.erase k, ‖A k j‖ = ∑ j ∈ Finset.univ.erase k, |A k j| := by
      apply Finset.sum_congr rfl
      intro j hj
      rfl
    -- Show ‖A k k‖ = A k k (since A k k > 0)
    have h_diag_norm : ‖A k k‖ = A k k := Real.norm_of_nonneg (le_of_lt (h_diag_pos k))
    rw [h_norm_eq, h_diag_norm]
    exact h_lt

  -- SUBGOAL_003: Conclude trivial solution
  have h_trivial : x = 0 := by
    -- Convert from sum form to matrix-vector form
    have h_mulVec : Matrix.mulVec A x = 0 := by
      funext i
      simp only [Matrix.mulVec, Matrix.dotProduct, Pi.zero_apply]
      exact hx i
    -- Apply the nonsingular matrix theorem
    exact Matrix.eq_zero_of_mulVec_eq_zero h_nonsingular h_mulVec

  -- Extract component-wise conclusion
  intro i
  rw [h_trivial]
  simp
